/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

/**
 * 模拟A2A客户端 - 用于性能测试
 *
 * 参考XuiA2AClient接口，只模拟流消息聊天功能，用于排除外部依赖进行准确的性能测试
 */

import { Logger } from '@/infrastructure/logger';
import { CancelTaskResponse, TaskIdParams } from '@a2a-js/sdk';

export interface MockA2AClientConfig {
    /** 模拟响应延迟 (ms) */
    responseDelay?: number;
    /** 模拟消息分块数量 */
    messageChunks?: number;
    /** 每个分块的延迟 (ms) */
    chunkDelay?: number;
    /** 模拟的消息内容 */
    mockMessage?: string;
}

export class MockA2AClient {
    private readonly config: Required<MockA2AClientConfig>;
    private customHeaders: Record<string, string> = {};
    private readonly requestId: string;

    constructor(
        private readonly agentServerUrl: string,
        config: MockA2AClientConfig = {}
    ) {
        this.config = {
            responseDelay: config.responseDelay ?? 100,
            messageChunks: config.messageChunks ?? 10,
            chunkDelay: config.chunkDelay ?? 200,
            mockMessage: config.mockMessage ?? 'This is a mock response from the simulated A2A agent. '
        };

        // 生成唯一的请求ID用于调试和追踪
        this.requestId = `mock_req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }

    public setHeaders(headers: Record<string, string>): void {
        this.customHeaders = { ...this.customHeaders, ...headers };
    }

    public getHeaders(): Record<string, string> {
        return { ...this.customHeaders };
    }

    public setHeader(key: string, value: string): void {
        this.customHeaders[key] = value;
    }

    /**
     * 模拟发送消息流 - 与XuiA2AClient接口保持一致
     */
    async *sendMessageStream(params: any): AsyncGenerator<any, void, undefined> {
        // 模拟日志记录（与真实客户端一致）
        Logger.info(`[${this.requestId}] Mock A2A Client - Sending request to ${this.agentServerUrl}`);

        // 从headers获取connect-time和llm-length配置
        const connectTime = this.parseConnectTime();
        const llmLength = this.parseLlmLength();

        Logger.info(`[${this.requestId}] Mock config - connect-time: ${connectTime}ms, llm-length: ${llmLength}`);

        // 记录开始时间
        const startTime = Date.now();

        // 模拟初始延迟
        await this.delay(this.config.responseDelay);

        // 发送任务开始事件
        yield this.createTaskStatusEvent(false);

        // 发送消息开始事件
        yield this.createArtifactStartEvent();

        // 生成指定长度的模拟消息
        const fullMessage = this.generateMockMessage(params, llmLength);
        const chunks = this.splitIntoChunks(fullMessage, this.config.messageChunks);

        // 模拟流式消息分块发送
        for (const chunk of chunks) {
            await this.delay(this.config.chunkDelay);
            yield this.createArtifactContentEvent(chunk);
        }

        // 发送消息结束事件
        yield this.createArtifactEndEvent();

        // 检查是否需要保持连接时长
        const elapsedTime = Date.now() - startTime;
        if (connectTime !== null && connectTime > 0 && elapsedTime < connectTime) {
            const remainingTime = connectTime - elapsedTime;
            Logger.info(`[${this.requestId}] Content finished, waiting ${remainingTime}ms to meet connect-time`);
            await this.delay(remainingTime);
        }

        // 发送任务完成事件
        yield this.createTaskStatusEvent(true);
    }

    /**
     * 从headers解析connect-time配置
     */
    private parseConnectTime(): number | null {
        const connectTimeHeader = this.customHeaders['connect-time'];
        if (connectTimeHeader === undefined || connectTimeHeader === '') {
            return null;
        }
        const connectTime = parseInt(connectTimeHeader, 10);
        return isNaN(connectTime) ? null : connectTime;
    }

    /**
     * 从headers解析llm-length配置
     */
    private parseLlmLength(): number | null {
        const llmLengthHeader = this.customHeaders['llm-length'];
        if (llmLengthHeader === undefined || llmLengthHeader === '') {
            return null;
        }
        const llmLength = parseInt(llmLengthHeader, 10);
        return isNaN(llmLength) ? null : llmLength;
    }

    /**
     * 生成模拟消息内容
     */
    private generateMockMessage(params: any, targetLength?: number | null): string {
        const userMessage = params?.message?.content?.[0]?.text ?? 'user message';
        const baseMessage = this.config.mockMessage;

        let fullMessage = `${baseMessage}You asked: "${userMessage}". ` +
            `Here's a detailed response that simulates the agent's processing. `;

        // 如果指定了llm-length，生成指定长度的内容
        if (targetLength !== null && targetLength !== undefined && targetLength > 0) {
            if (fullMessage.length < targetLength) {
                // 内容不够长，重复填充
                const repeatCount = Math.ceil(targetLength / fullMessage.length);
                fullMessage = fullMessage.repeat(repeatCount);
            }
            // 截取到指定长度
            fullMessage = fullMessage.substring(0, targetLength);
        } else {
            // 默认重复2次
            fullMessage = fullMessage.repeat(2);
        }

        return fullMessage;
    }

    /**
     * 将消息分割成块
     */
    private splitIntoChunks(message: string, chunkCount: number): string[] {
        const chunkSize = Math.ceil(message.length / chunkCount);
        const chunks: string[] = [];

        for (let i = 0; i < message.length; i += chunkSize) {
            chunks.push(message.slice(i, i + chunkSize));
        }

        return chunks;
    }

    /**
     * 创建任务状态更新事件
     */
    private createTaskStatusEvent(final: boolean): Record<string, any> {
        return {
            kind: 'status-update',
            final,
            status: {
                "message": {
                    "contextId": "b2e0f5b7-0a9f-4617-af6b-f978af21d6a0",
                    "kind": "message",
                    "messageId": "f21d38f9-ffec-4dde-a7f9-32d9495b0cf6",
                    "parts": [
                        {
                            "kind": "text",
                            "text": "很高兴见到你。"
                        }
                    ],
                    "role": "agent",
                    "taskId": "23fa7c7f-898a-4b1e-ad0c-333002e92ff7"
                },
                "state": "working",
                timestamp: new Date().toISOString()
            }
        };
    }

    /**
     * 创建工件开始事件
     */
    private createArtifactStartEvent(): Record<string, any> {
        return {
            kind: 'artifact-update',
            artifact: {
                parts: [{
                    kind: 'data',
                    data: {
                        type: 'PART_START',
                        partType: 'TEXT'
                    }
                }]
            }
        };
    }

    /**
     * 创建工件内容事件
     */
    private createArtifactContentEvent(content: string): Record<string, any> {
        return {
            kind: 'artifact-update',
            artifact: {
                parts: [{
                    kind: 'data',
                    data: {
                        type: 'PART_CONTENT',
                        partType: 'TEXT',
                        content: {
                            contentType: 'text',
                            value: content
                        }
                    }
                }]
            }
        };
    }

    /**
     * 创建工件结束事件
     */
    private createArtifactEndEvent(): Record<string, any> {
        return {
            kind: 'artifact-update',
            artifact: {
                parts: [{
                    kind: 'data',
                    data: {
                        type: 'PART_END',
                        partType: 'TEXT'
                    }
                }]
            }
        };
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 模拟取消任务
     */
    async cancelTask(params: TaskIdParams): Promise<CancelTaskResponse> {
        return {...params} as CancelTaskResponse;
    }
}
