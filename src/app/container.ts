/**
 * TSyringe 依赖注入容器
 * 
 * 使用TSyringe实现的现代化依赖注入容器，
 * 支持装饰器和自动解析。
 */

import { container } from 'tsyringe';
import { databaseService } from '@/infrastructure/database';
import getLangfuseService from '@/infrastructure/logger/langfuse';
import { Logger } from '@/infrastructure/logger/winston-logger';

// Import repositories
import { AgentRepository } from '@/modules/agent/repositories/agent-repository';
import { MessageRepository } from '@/modules/message/repositories/message-repository';
import { SessionRepository } from '@/modules/session/repositories/session-repository';
import { UserStorageRepository } from '@/modules/user-storage/repositories/user-storage-repository';

// Import services
import { AgentService } from '@/modules/agent/services/agent-service';
import { MessageService } from '@/modules/message/services/message-service';
import { SessionService } from '@/modules/session/services/session-service';
import { ChatService } from '@/modules/session/services/chat-service';
import { ChatMockService } from '@/modules/session/services/chat-mock-service';
import { HealthService } from '@/modules/health/services/health-service';
import { UserStorageService } from '@/modules/user-storage/services/user-storage-service';

// Import mappers
import { AgentMapper } from '@/modules/agent/services/agent-mapper';
import { MessageMapper } from '@/modules/message/services/message-mapper';
import { SessionMapper } from '@/modules/session/services/session-mapper';
import { UserStorageMapper } from '@/modules/user-storage/services/user-storage-mapper';

// Import controllers
import { AgentController } from '@/modules/agent/controllers/agent-controller';
import { MessageController } from '@/modules/message/controllers/message-controller';
import { SessionController } from '@/modules/session/controllers/session-controller';
import { HealthController } from '@/modules/health/controllers/health-controller';
import { ChatController } from '@/modules/session/controllers/chat-controller';
import { ChatMockController } from '@/modules/session/controllers/chat-mock-controller';
import { UserStorageController } from '@/modules/user-storage/controllers/user-storage-controller';

// Import service identifiers
import { TYPES } from '@/shared/constants';

/**
 * TSyringe容器配置类
 */
export class ContainerConfig {
    private static initialized = false;

    /**
     * 初始化容器和所有服务
     */
    public static async initialize(): Promise<void> {
        if (this.initialized) {
            Logger.info('Container already initialized');
            return;
        }

        try {
            Logger.info('Initializing TSyringe container...');

            // 初始化已注册的服务
            await this.initializeServices();

            this.initialized = true;
            Logger.info('TSyringe container initialized successfully');

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error('Failed to initialize TSyringe container', { error: errorMessage });
            throw error;
        }
    }

    /**
     * 注册所有服务到容器
     */
    public static registerServices(): void {
        // 注册数据库服务
        container.registerInstance(TYPES.DatabaseService, databaseService);

        // 注册Langfuse服务（始终注册，即使被禁用）
        const langfuse = getLangfuseService();
        container.registerInstance(TYPES.LangfuseService, langfuse);
        Logger.info('Langfuse service registered');

        // 注册Logger
        container.registerInstance(TYPES.Logger, Logger);

        // 注册 Repositories
        container.registerSingleton(TYPES.AgentRepository, AgentRepository);
        container.registerSingleton(TYPES.MessageRepository, MessageRepository);
        container.registerSingleton(TYPES.SessionRepository, SessionRepository);
        container.registerSingleton(TYPES.UserStorageRepository, UserStorageRepository);

        // 注册 Mappers
        container.registerSingleton(TYPES.AgentMapper, AgentMapper);
        container.registerSingleton(TYPES.MessageMapper, MessageMapper);
        container.registerSingleton(TYPES.SessionMapper, SessionMapper);
        container.registerSingleton(TYPES.UserStorageMapper, UserStorageMapper);

        // 注册 Services
        container.registerSingleton(TYPES.AgentService, AgentService);
        container.registerSingleton(TYPES.MessageService, MessageService);
        container.registerSingleton(TYPES.SessionService, SessionService);
        container.registerSingleton(TYPES.ChatService, ChatService);
        container.registerSingleton(TYPES.ChatMockService, ChatMockService);
        container.registerSingleton(TYPES.HealthService, HealthService);
        container.registerSingleton(TYPES.UserStorageService, UserStorageService);

        // 注册 Controllers
        container.registerSingleton(TYPES.AgentController, AgentController);
        container.registerSingleton(TYPES.MessageController, MessageController);
        container.registerSingleton(TYPES.SessionController, SessionController);
        container.registerSingleton(TYPES.HealthController, HealthController);
        container.registerSingleton(TYPES.ChatController, ChatController);
        container.registerSingleton(TYPES.ChatMockController, ChatMockController);
        container.registerSingleton(TYPES.UserStorageController, UserStorageController);
        Logger.info('All services registered to TSyringe container');
    }

    /**
     * 初始化所有需要初始化的服务
     */
    private static async initializeServices(): Promise<void> {
        try {
            // 确保数据库服务已初始化（可能已经在bootstrap中初始化了）
            const dbService = container.resolve<typeof databaseService>(TYPES.DatabaseService);
            await dbService.ensureInitialized();
            Logger.info('Database service initialization confirmed');

            // 不需要初始化其他服务，因为它们已经在各自的模块中初始化了

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error('Failed to initialize services', { error: errorMessage });
            throw error;
        }
    }

    /**
     * 销毁容器和所有服务
     */
    public static async destroy(): Promise<void> {
        if (!this.initialized) {
            Logger.info('Container not initialized, nothing to destroy');
            return;
        }

        try {
            Logger.info('Destroying TSyringe container...');

            // 清理容器
            container.clearInstances();

            this.initialized = false;
            Logger.info('TSyringe container destroyed successfully');

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            Logger.error('Error during TSyringe container cleanup', { error: errorMessage });
            throw error;
        }
    }

    /**
     * 获取服务实例
     */
    public static get<T>(identifier: string): T {
        if (!this.initialized) {
            throw new Error('Container not initialized. Call initialize() first.');
        }
        return container.resolve<T>(identifier);
    }

    /**
     * 检查容器是否已初始化
     */
    public static isInitialized(): boolean {
        return this.initialized;
    }
}

// 导出TSyringe容器实例以供直接使用
export { container as tsyringeContainer };

// 注意：不在模块加载时自动注册服务，而是在应用启动时显式调用
// 这确保配置已经完全加载后再初始化服务