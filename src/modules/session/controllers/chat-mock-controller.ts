/**
 * Chat Controller
 *
 * Handles HTTP requests for chat operations.
 */
import { injectable, inject } from 'tsyringe';
import { createSession, type Session as SSESession } from 'better-sse';
import { BaseController } from '@/shared/utils/base-controller';
import { ChatMockService } from '../services/chat-mock-service';
import { TYPES } from '@/shared/constants';
import type { ChatRequestDto } from '../dto/session-dto';
import type { AuthenticatedRequest } from '@/shared/types';
import type { Response } from 'express';

@injectable()
export class ChatMockController extends BaseController {
    constructor(
        @inject(TYPES.ChatMockService) private readonly chatMockService: ChatMockService,
    ) {
        super();
    }

    /**
     * chat - 优化的实时SSE流式传输
     */
    public chat = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const chatRequestDto = req.validatedBody as ChatRequestDto;
            const userId = this.getUserId(req);

            // 创建better-sse会话，配置立即推送
            let sseSession = await createSession(req, res);

            // 验证连接
            if (!sseSession.isConnected) {
                throw new Error('Failed to establish SSE connection');
            }

            // 立即发送一个连接确认事件
            sseSession.push('Connected', 'connection');

            // 处理聊天请求
            await this.chatMockService.chat(sessionId, userId, chatRequestDto, req, res, sseSession!);

            // 手动清理SSE对象和相关引用，释放内存
            try {
                // 确保响应已结束
                // if (!res.writableEnded) {
                //     res.end();
                // }

                // // 清理请求和响应对象的可能引用
                // // 移除事件监听器，避免循环引用
                // req.removeAllListeners();
                // res.removeAllListeners();

                // // 强制垃圾回收提示（如果可用）
                // if (global.gc) {
                //     setTimeout(() => {
                //         global.gc?.();
                //     }, 0);
                // }
            } catch {
                // 清理失败不应该影响正常流程，静默处理
            }
        }
    );
}