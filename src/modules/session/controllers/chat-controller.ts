/**
 * Chat Controller
 *
 * Handles HTTP requests for chat operations.
 */
import { injectable, inject } from 'tsyringe';
import { createSession } from 'better-sse';
import { BaseController } from '@/shared/utils/base-controller';
import { ChatService } from '../services/chat-service';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { TYPES } from '@/shared/constants';
import type { ChatRequestDto } from '../dto/session-dto';
import type { AuthenticatedRequest, TypedResponse } from '@/shared/types';
import type { Response } from 'express';

@injectable()
export class ChatController extends BaseController {
    constructor(
        @inject(TYPES.ChatService) private readonly chatService: ChatService,
    ) {
        super();
    }

    /**
     * chat - 优化的实时SSE流式传输
     */
    public chat = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const chatRequestDto = req.validatedBody as ChatRequestDto;
            const userId = this.getUserId(req);

            // 创建better-sse会话，配置立即推送
            const sseSession = await createSession(req, res);

            // 验证连接
            if (!sseSession.isConnected) {
                throw new Error('Failed to establish SSE connection');
            }

            // 立即发送一个连接确认事件
            sseSession.push('Connected', 'connection');

            // 处理聊天请求 - 不等待完成，让它在后台运行
            // 这样controller方法可以立即结束，释放内存
            this.chatService.chat(sessionId, userId, chatRequestDto, req, res, sseSession)
                .catch(error => {
                    // 记录后台处理中的错误，但不影响controller的返回
                    Logger.error('Background chat processing error', {
                        error: error instanceof Error ? error.message : String(error),
                        sessionId,
                        userId,
                        agentId: chatRequestDto.agentId
                    });
                });

            // Controller方法立即结束，释放内存
            // SSE连接会由chatService管理和关闭
        }
    );

    /**
     * Cancel a session
     */

    public chatCancel = this.asyncHandler(
        async (req: AuthenticatedRequest, res: TypedResponse<{sessionId: string}>): Promise<void> => {
            const { sessionId } = req.validatedParams as { sessionId: string };
            const { agentId } = req.validatedBody as { agentId: string };
            const userId = this.getUserId(req);
            const cancelResult = await this.chatService.cancelChat(sessionId, userId, agentId);
            if ('error' in cancelResult) {
                this.sendError(res as TypedResponse<unknown>, 'Failed to cancel chat', cancelResult);
                return;
            }
            this.sendSuccess(res, {sessionId}, 'Session canceled successfully');
        }
    );
}