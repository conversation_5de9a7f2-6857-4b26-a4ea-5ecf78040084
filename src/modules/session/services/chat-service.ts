/**
 * Chat Service
 *
 * Handles chat operations by communicating with external agents via A2A protocol.
 * Provides clean, structured methods for chat session management and stream processing.
 */
import { injectable, inject } from 'tsyringe';
import {
    MessageSendParams,
    TaskArtifactUpdateEvent,
    TaskStatusUpdateEvent,
    Message as A2AMessage,
    Artifact as A2AArtifact,
    Task,
    TaskIdParams,
    CancelTaskResponse
} from '@a2a-js/sdk';
import { XuiA2AClient } from '@/shared/utils/xui-a2a-client';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { TYPES } from '@/shared/constants';

import type { Session as SSESession } from 'better-sse';
import type { ChatRequestDto } from '@/modules/session/dto';
import { SessionService } from './session-service';
import { SessionRepository } from '../repositories/session-repository';
import {
    convertA2AMessageToA2U,
    convertA2UMessageToA2A,
    createMessageEndEvent,
    createMessageStartEvent,
    createPartContentEvent,
    createPartEndEvent,
    createPartStartEvent,
    createSessionErrorEvent,
    createSessionFinishEvent,
    createSessionStartEvent,
    eventDelta
} from '@/shared/utils/message-converter';
import { A2UUserMessage } from '@xui-web-app/a2u';
import type { Request, Response } from 'express';
import { MessageService } from '@/modules/message/services/message-service';
import { CreateMessageRequestDto, UpdateMessageRequestDto } from '@/modules/message/dto/message-dto';
import type { MessageContent as DBA2UMessageContent } from '@/infrastructure/database/schema/message';
import { ChatSession } from './chat-session';
import { IncomingHttpHeaders } from 'http';
import { ValidationError, ResourceNotFoundError } from '@/shared/utils';

@injectable()
export class ChatService {
    constructor(
        @inject(TYPES.SessionService) private readonly sessionService: SessionService,
        @inject(TYPES.MessageService) private readonly messageService: MessageService,
        @inject(TYPES.SessionRepository) private readonly sessionRepository: SessionRepository
    ) { }

    /**
     * 处理聊天请求的主要方法
     * 协调整个聊天流程：验证、保存消息、建立连接、处理流数据
     */
    public async chat(
        sessionId: string,
        userId: string,
        chatRequestDto: ChatRequestDto,
        req: Request,
        res: Response,
        sseSession: SSESession,
    ): Promise<void> {
        const chatSession = new ChatSession(sessionId, userId, chatRequestDto.agentId);
        chatSession.initialize();

        try {
            // 1. 验证会话和代理
            const sessionInfo = await this.validateSessionAndAgent(sessionId, userId, chatRequestDto.agentId);

            // 2. 保存用户消息到数据库
            await this.saveUserMessage(chatSession, chatRequestDto);

            // 3. 发送会话开始事件
            this.sendSessionStartEvent(sessionId, sseSession);

            // 4. 建立并处理 A2A 通信
            await this.handleA2ACommunication(
                sseSession,
                sessionId,
                userId,
                chatSession,
                chatRequestDto,
                sessionInfo,
                req
            );

            // 5. 保存代理响应到数据库
            await this.saveAgentMessage(chatSession);

            // 6. 发送会话完成事件
            this.sendSessionFinishEvent(sseSession, res);

        } catch (error) {
            await this.handleChatError(
                error,
                sessionId,
                userId,
                sseSession,
                chatRequestDto.agentId,
                res,
                chatSession
            );
        }
    }

    // ==================== SSE 事件处理方法 ====================

    /**
     * 安全地向 SSE 会话推送数据
     */
    private pushToSSE(sseSession: SSESession, data: unknown, eventType: string): void {
        if (sseSession.isConnected) {
            sseSession.push(data, eventType);
        }
    }

    /**
     * 发送会话开始事件
     */
    private sendSessionStartEvent(sessionId: string, sseSession: SSESession): void {
        const sessionStartEvent = createSessionStartEvent(sessionId, '');
        this.pushToSSE(sseSession, sessionStartEvent, 'session-start');
    }

    /**
     * 发送会话完成事件并关闭连接
     */
    private sendSessionFinishEvent(sseSession: SSESession, res: Response): void {
        const sessionFinishEvent = createSessionFinishEvent('');
        try {
            this.pushToSSE(sseSession, sessionFinishEvent, 'session-finish');
        } finally {
            if (!res.writableEnded) {
                res.end();
            }
        }
    }

    /**
     * 发送错误事件并关闭连接
     */
    private sendErrorEvent(sseSession: SSESession, sessionId: string, errorMessage: string): void {
        const sessionErrorEvent = createSessionErrorEvent(sessionId, errorMessage);
        try {
            this.pushToSSE(sseSession, sessionErrorEvent, 'session-error');
        } catch (error) {
            Logger.warn(`Failed to send error event: ${(error as Error).message}`, {
                sessionId,
                method: 'sendErrorEvent'
            });
        }
    }

    // ==================== 验证方法 ====================

    /**
     * 验证会话和代理的有效性
     */
    private async validateSessionAndAgent(
        sessionId: string,
        userId: string,
        agentId: string,
    ): Promise<{ agentServerUrl: string; taskId: string | null; taskState: string | null }> {
        const result = await this.sessionRepository.findByIdWithFullAgentInfo(sessionId, userId, agentId);

        if (!result) {
            throw new ResourceNotFoundError(
                'Session or Agent',
                `Session with ID ${sessionId} or Agent with ID ${agentId} not found.`
            );
        }

        const { session: sessionEntity, agent } = result;
        const cardUrl = agent.cardUrl;

        if (cardUrl === null) {
            throw new ValidationError(`Agent with ID ${agentId} does not have a configured card URL.`);
        }

        return {
            agentServerUrl: cardUrl,
            taskId: sessionEntity.taskId,
            taskState: sessionEntity.taskState,
        };
    }

    // ==================== A2A 通信方法 ====================

    /**
     * 处理 A2A 通信
     */
    private async handleA2ACommunication(
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        chatSession: ChatSession,
        chatRequestDto: ChatRequestDto,
        sessionInfo: { agentServerUrl: string; taskId: string | null; taskState: string | null },
        req: Request
    ): Promise<void> {
        // 准备 A2A 参数
        const a2aParams = this.prepareA2AParams(chatRequestDto, chatSession, sessionInfo);

        // 创建 A2A 客户端
        const client = this.createA2AClient(sessionInfo.agentServerUrl, chatSession, req.headers);

        // 建立流连接
        const agentStream = await this.establishA2AStream(client, a2aParams, sseSession, sessionId);
        if (!agentStream) {
            return; // 连接失败，错误已处理
        }

        // 处理流数据
        await this.processA2AStream(sseSession, sessionId, userId, chatSession, agentStream, chatRequestDto.agentId);
    }

    /**
     * 准备 A2A 参数
     */
    private prepareA2AParams(
        chatRequestDto: ChatRequestDto,
        chatSession: ChatSession,
        sessionInfo: { taskId: string | null; taskState: string | null }
    ): MessageSendParams {
        const a2aParams = this.getA2AParamsFromMessage(chatRequestDto);
        chatSession.messageId = a2aParams.message.messageId;

        // 设置任务 ID（如果需要）
        if (sessionInfo.taskId !== null && sessionInfo.taskState !== null &&
            (sessionInfo.taskState === 'input-required' || sessionInfo.taskState === 'auth-required')) {
            a2aParams.message.taskId = sessionInfo.taskId;
        }

        // 设置上下文 ID
        a2aParams.message.contextId = chatSession.sessionId;

        return a2aParams;
    }

    /**
     * 创建 A2A 客户端并设置请求头
     */
    private createA2AClient(
        agentServerUrl: string,
        chatSession: ChatSession,
        headers: IncomingHttpHeaders
    ): XuiA2AClient {
        const client = new XuiA2AClient(agentServerUrl);
        this.setA2AHeaders(client, chatSession, headers);
        return client;
    }

    /**
     * 建立 A2A 流连接
     */
    private async establishA2AStream(
        client: XuiA2AClient,
        a2aParams: MessageSendParams,
        sseSession: SSESession,
        sessionId: string
    ): Promise<AsyncGenerator<unknown, void, unknown> | null> {
        try {
            return client.sendMessageStream(a2aParams);
        } catch (error) {
            Logger.warn(`Failed to establish A2A stream: ${(error as Error).message}`, {
                sessionId,
                method: 'establishA2AStream'
            });

            const errorMessage = `Failed to establish connection to agent: ${(error as Error).message}`;
            this.pushToSSE(sseSession, createSessionErrorEvent(sessionId, errorMessage), 'session-error');
            return null;
        }
    }

    // ==================== 错误处理方法 ====================

    /**
     * 处理聊天过程中的错误
     */
    private async handleChatError(
        error: unknown,
        sessionId: string,
        userId: string,
        sseSession: SSESession,
        agentId: string,
        res: Response,
        chatSession: ChatSession
    ): Promise<void> {
        try {
            // 记录错误
            Logger.warn(`Chat error: ${(error as Error).message}`, {
                sessionId,
                agentId,
                userId,
                method: 'chat'
            });

            // 发送错误事件
            this.sendErrorEvent(sseSession, sessionId, (error as Error).message);

            // 清理：尝试删除未完成的消息
            await this.cleanupFailedMessage(chatSession, userId);

        } catch (finalError) {
            // 最后的错误处理
            Logger.warn(`Final error in chat error handling: ${(finalError as Error).message}`, {
                sessionId,
                agentId,
                userId
            });
        } finally {
            // 强制结束响应
            this.sendSessionFinishEvent(sseSession, res);
        }
    }

    /**
     * 清理失败的消息
     */
    private async cleanupFailedMessage(chatSession: ChatSession, userId: string): Promise<void> {
        if (chatSession.dbId) {
            try {
                await this.messageService.deleteMessage(chatSession.dbId, userId);
                chatSession.dbId = '';
            } catch (deleteError) {
                Logger.warn(`Failed to delete message during cleanup: ${(deleteError as Error).message}`, {
                    dbId: chatSession.dbId,
                    userId
                });
            }
        }
    }

    // ==================== 流数据处理方法 ====================

    /**
     * 处理 A2A 流数据并通过 SSE 推送给客户端
     */
    private async processA2AStream(
        sseSession: SSESession,
        sessionId: string,
        userId: string,
        chatSession: ChatSession,
        agentStream: AsyncGenerator<unknown, void, unknown>,
        agentId: string
    ): Promise<void> {
        // 发送消息开始事件
        const messageStartEvent = createMessageStartEvent(
            chatSession.messageId,
            { id: agentId },
            chatSession.dbId
        );
        this.pushToSSE(sseSession, messageStartEvent, 'message-start');

        try {
            for await (const event of agentStream) {
                Logger.info(`Processing A2A stream event: ${JSON.stringify(event)}`, {
                    userId,
                    sessionId,
                    agentId,
                    method: 'processA2AStream'
                });

                // 更新会话任务状态
                await this.updateSessionTaskIdAndState(event, sessionId, agentId);

                // 检查事件是否包含错误信息
                if (this.isErrorEvent(event)) {
                    this.handleStreamError(sseSession, sessionId, event);
                    break;
                }

                // 处理正常的流事件
                try {
                    this.processA2AStreamEvent(sseSession, chatSession, event, agentId);
                } catch (eventError) {
                    Logger.warn(`A2A stream event processing error: ${(eventError as Error).message}`, {
                        userId,
                        sessionId,
                        agentId,
                        method: 'processStreamEvent'
                    });
                    this.sendErrorEvent(sseSession, sessionId, (eventError as Error).message);
                    break;
                }
            }
        } catch (streamError) {
            this.handleStreamConnectionError(sseSession, streamError, {
                userId,
                sessionId,
                agentId
            });
        }
    }

    /**
     * 检查事件是否为错误事件
     */
    private isErrorEvent(event: unknown): boolean {
        return event !== null && typeof event === 'object' && 'error' in event;
    }

    /**
     * 处理流错误事件
     */
    private handleStreamError(sseSession: SSESession, sessionId: string, event: unknown): void {
        const errorEvent = event as { error: { code: number; message: string; data?: unknown } };

        Logger.warn(`A2A stream error: ${errorEvent.error.message}`, {
            sessionId,
            method: 'handleStreamError'
        });

        const errorMessage = `SSE event contained an error: ${errorEvent.error.message} ` +
                             `(Code: ${errorEvent.error.code})`;
        this.pushToSSE(sseSession, createSessionErrorEvent(sessionId, errorMessage), 'session-error');
    }

    /**
     * 处理流连接错误
     */
    private handleStreamConnectionError(
        sseSession: SSESession,
        error: unknown,
        context: { userId: string; sessionId: string; agentId: string }
    ): void {
        Logger.warn(`A2A stream connection error: ${(error as Error).message}`, {
            ...context,
            method: 'handleStreamConnectionError'
        });

        const errorMessage = (error as Error).message;
        let userFriendlyMessage: string;

        if (errorMessage.includes('terminated') ||
            errorMessage.includes('other side closed') ||
            errorMessage.includes('socket') ||
            (error as Error).name === 'TypeError') {
            userFriendlyMessage = 'Connection to agent service was closed unexpectedly';
        } else {
            userFriendlyMessage = `A2A stream error: ${errorMessage}`;
        }

        this.pushToSSE(sseSession, createSessionErrorEvent('', userFriendlyMessage), 'session-error');
    }

    /**
     * 处理 A2A 流事件
     */
    private processA2AStreamEvent(
        sseSession: SSESession,
        chatSession: ChatSession,
        a2aEvent: unknown,
        agentId: string,
    ): void {
        const event = a2aEvent as { kind: string; [key: string]: unknown };

        switch (event.kind) {
            case 'task':
                // 暂不做处理
                break;
            case 'status-update':
                this.handleStatusUpdate(sseSession, chatSession, a2aEvent as TaskStatusUpdateEvent, agentId);
                break;
            case 'artifact-update':
                this.handleArtifactUpdate(sseSession, chatSession, a2aEvent as TaskArtifactUpdateEvent, agentId);
                break;
            default:
                this.handleMessageEvent(sseSession, chatSession, a2aEvent as A2AMessage, agentId);
                break;
        }
    }

    /**
     * 处理状态更新事件
     */
    private handleStatusUpdate(
        sseSession: SSESession,
        chatSession: ChatSession,
        statusEvent: TaskStatusUpdateEvent,
        agentId: string
    ): void {
        if (statusEvent.final) {
            const messageEndEvent = createMessageEndEvent(chatSession.messageId);
            this.pushToSSE(sseSession, messageEndEvent, 'message-end');
        } else if (statusEvent.status.message) {
            this.processPartData(sseSession, chatSession, statusEvent.status.message, agentId);
        }
    }

    /**
     * 处理工件更新事件
     */
    private handleArtifactUpdate(
        sseSession: SSESession,
        chatSession: ChatSession,
        artifactEvent: TaskArtifactUpdateEvent,
        agentId: string
    ): void {
        const firstPart = artifactEvent.artifact.parts[0];

        if (this.isPartStartEvent(firstPart)) {
            this.handlePartStart(sseSession, chatSession, firstPart);
        } else if (this.isPartEndEvent(firstPart)) {
            this.handlePartEnd(sseSession, chatSession);
        } else {
            this.processPartData(sseSession, chatSession, artifactEvent.artifact, agentId);
        }
    }

    /**
     * 处理消息事件
     */
    private handleMessageEvent(
        sseSession: SSESession,
        chatSession: ChatSession,
        messageEvent: A2AMessage,
        agentId: string
    ): void {
        this.processPartData(sseSession, chatSession, messageEvent, agentId);
    }

    /**
     * 检查是否为部分开始事件
     */
    private isPartStartEvent(part: unknown): boolean {
        return part !== null &&
               typeof part === 'object' &&
               'kind' in part &&
               part.kind === 'data' &&
               'data' in part &&
               typeof part.data === 'object' &&
               part.data !== null &&
               'type' in part.data &&
               part.data.type === 'PART_START';
    }

    /**
     * 检查是否为部分结束事件
     */
    private isPartEndEvent(part: unknown): boolean {
        return part !== null &&
               typeof part === 'object' &&
               'kind' in part &&
               part.kind === 'data' &&
               'data' in part &&
               typeof part.data === 'object' &&
               part.data !== null &&
               'type' in part.data &&
               part.data.type === 'PART_END';
    }

    /**
     * 处理部分开始
     */
    private handlePartStart(sseSession: SSESession, chatSession: ChatSession, firstPart: unknown): void {
        chatSession.partIndex++;

        // 类型安全地提取 partType
        const partType = this.extractPartType(firstPart);
        const contentType = partType.toLowerCase() as 'text' | 'file' | 'data';
        chatSession.partType = contentType;
        chatSession.tags.add(contentType);

        const partStartEvent = createPartStartEvent(chatSession.partIndex, contentType);
        this.pushToSSE(sseSession, partStartEvent, 'part-start');

        // 根据内容类型创建正确的消息内容对象
        this.initializeMessageContent(chatSession, contentType);
    }

    /**
     * 安全地提取 partType
     */
    private extractPartType(part: unknown): string {
        if (part !== null &&
            typeof part === 'object' &&
            'data' in part &&
            typeof part.data === 'object' &&
            part.data !== null &&
            'partType' in part.data &&
            typeof part.data.partType === 'string') {
            return part.data.partType;
        }
        return 'text'; // 默认值
    }

    /**
     * 处理部分结束
     */
    private handlePartEnd(sseSession: SSESession, chatSession: ChatSession): void {
        const partEndEvent = createPartEndEvent(chatSession.partIndex);
        this.pushToSSE(sseSession, partEndEvent, 'part-end');

        if (chatSession.a2uMessageContent) {
            chatSession.agentMessage?.content.push(chatSession.a2uMessageContent);
            chatSession.a2uMessageContent = undefined;
        }
    }

    /**
     * 初始化消息内容对象
     */
    private initializeMessageContent(chatSession: ChatSession, contentType: 'text' | 'file' | 'data'): void {
        switch (contentType) {
            case 'text':
                chatSession.a2uMessageContent = { type: 'text', text: '' };
                break;
            case 'file':
                chatSession.a2uMessageContent = { type: 'file' } as DBA2UMessageContent;
                break;
            case 'data':
                chatSession.a2uMessageContent = { type: 'data' } as DBA2UMessageContent;
                break;
        }
    }

    private processPartData(
        sseSession: SSESession,
        chatSession: ChatSession,
        message: A2AMessage | A2AArtifact,
        agentId: string,
    ): void {
        const a2uMessage = convertA2AMessageToA2U(message, agentId);

        // 根据content数组第一条内容的type动态设置参数
        const firstContent = a2uMessage.content[0];
        if(a2uMessage.extendedData && chatSession.agentMessage) {
            chatSession.agentMessage.extendedData = a2uMessage.extendedData;
        }
        let partContentEvent;

        if (firstContent?.type === 'text') {
            let delta: eventDelta;

            if (chatSession.partType === 'text') {
                delta = {
                    type: 'text_delta',
                    text: firstContent.text,
                }
            } else {
                delta = {
                    type: 'data_delta',
                    data: firstContent.text,
                }
            }

            if(firstContent.extendedData) {
                delta.extendedData = firstContent.extendedData;
            };

            partContentEvent = createPartContentEvent(chatSession.partIndex, delta);

            chatSession.updateMessageContent('text', firstContent.text || '');
        } else if (firstContent?.type === 'file') {
            const fileDelta: eventDelta = {
                type: 'file_delta' as const,
                file: firstContent.file,
                ...(firstContent.extendedData !== undefined && { extendedData: firstContent.extendedData })
            };
            partContentEvent = createPartContentEvent(chatSession.partIndex, fileDelta);
            chatSession.updateMessageContent('file', firstContent.file);
        } else if (firstContent?.type === 'data') {
            const dataDelta: eventDelta = {
                type: 'data_delta' as const,
                data: JSON.stringify(firstContent.data),
                ...(firstContent.extendedData !== undefined && { extendedData: firstContent.extendedData })
            };
            partContentEvent = createPartContentEvent(chatSession.partIndex, dataDelta);

            chatSession.updateMessageContent('data', JSON.stringify(firstContent.data));
        } else {
            // 默认情况，保持原有逻辑
            partContentEvent = createPartContentEvent(
                chatSession.partIndex,
                { type: 'data_delta' as const, data: JSON.stringify(a2uMessage.content) }
            );
            chatSession.updateMessageContent('data', JSON.stringify(a2uMessage.content));
        }

        if(a2uMessage.extendedData) {
            (partContentEvent as Record<string, unknown>)['extendedData'] = a2uMessage.extendedData;
        }
        this.pushToSSE(sseSession, partContentEvent, 'part-content');
    }

    /**
     * getA2AParamsFromMessage
     */
    private getA2AParamsFromMessage(chatRequestDto: ChatRequestDto): MessageSendParams {
        const { message } = chatRequestDto;

        const userMessage: A2UUserMessage = message as A2UUserMessage;

        return convertA2UMessageToA2A(userMessage as never);
    }

    // ==================== 数据库操作方法 ====================

    /**
     * 保存用户消息到数据库
     */
    private async saveUserMessage(
        chatSession: ChatSession,
        chatRequestDto: ChatRequestDto
    ): Promise<void> {
        const { message } = chatRequestDto;

        // 保存用户消息
        const userMessageWithSessionId = {
            ...message,
            sessionId: chatSession.sessionId,
            userId: chatSession.userId,
        };
        await this.messageService.createMessage(userMessageWithSessionId as CreateMessageRequestDto);

        // 创建代理消息占位符
        const agentMessage = await this.messageService.createMessage(
            { ...chatSession.agentMessage, userId: chatSession.userId } as CreateMessageRequestDto
        );

        if (agentMessage.dbId) {
            chatSession.dbId = agentMessage.dbId;
        }
    }

    /**
     * 保存代理消息到数据库
     */
    private async saveAgentMessage(chatSession: ChatSession): Promise<void> {
        if (chatSession.agentMessage && chatSession.tags.size > 0) {
            chatSession.agentMessage.tags = Array.from(chatSession.tags);
        }

        // 只有当 dbId 存在时才更新
        if (chatSession.dbId && chatSession.dbId.trim() !== '') {
            await this.messageService.updateMessage(
                chatSession.dbId,
                chatSession.userId,
                {
                    ...chatSession.agentMessage,
                    updatedAt: new Date()
                } as UpdateMessageRequestDto
            );
        }
    }

    private setA2AHeaders(client: XuiA2AClient, chatSession: ChatSession, headers: IncomingHttpHeaders): void {
        if (chatSession.userId) {
            client.setHeader('userId', chatSession.userId);
        }

        if (headers['token'] !== undefined) {
            client.setHeader('token', headers['token'] as string);
        }

        if (headers['user'] !== undefined) {
            client.setHeader('user', headers['user'] as string);
        }

    }

    private async updateSessionTaskIdAndState(
        event: unknown,
        sessionId: string,
        agentId: string
    ): Promise<void> {
        // 检测task状态
        if (event !== null && typeof event === 'object' && 'kind' in event) {
            if (event.kind === 'task' || event.kind === 'status-update') {
                let taskEvent = event as unknown as (Task | TaskStatusUpdateEvent);
                if (
                    taskEvent.status.state === 'input-required' ||
                    taskEvent.status.state === 'auth-required' ||
                    taskEvent.status.state === 'submitted'
                ) {
                    try {
                        if (event.kind === 'task') {
                            taskEvent = event as unknown as Task;
                            await this.sessionService.addTaskIdAndState(
                                sessionId,
                                taskEvent.id,
                                taskEvent.status.state as 'submitted' | 'input-required' | 'auth-required'
                            );
                        }
                        if (event.kind === 'status-update') {
                            taskEvent = event as unknown as TaskStatusUpdateEvent;
                            await this.sessionService.addTaskIdAndState(
                                sessionId,
                                taskEvent.taskId,
                                taskEvent.status.state as 'submitted' | 'input-required' | 'auth-required'
                            );
                        }
                    } catch (error) {
                        // 更新会话任务状态失败记录为warn级别
                        const logContext = {
                            sessionId,
                            agentId,
                            method: 'updateSessionTaskIdAndState',
                            url: '/chat'
                        };
                        Logger.warn(`Failed to update session task state: ${(error as Error).message}`, logContext);
                    }
                } else if (
                    taskEvent.status.state === 'completed' ||
                    taskEvent.status.state === 'failed' ||
                    taskEvent.status.state === 'canceled' ||
                    taskEvent.status.state === 'rejected' ||
                    taskEvent.status.state === 'unknown'
                ) {
                    try {
                        await this.sessionService.clearTaskIdAndState(sessionId);
                    } catch (error) {
                        Logger.warn(`Failed to clear session task state: ${(error as Error).message}`, {
                            agentId,
                            sessionId,
                            method: 'clearSessionTaskIdAndState'
                        });
                    }
                }
            }
        }
    }

    // ==================== 公共方法 ====================

    /**
     * 取消聊天任务
     */
    public async cancelChat(
        sessionId: string,
        userId: string,
        agentId: string
    ): Promise<CancelTaskResponse> {
        // 验证会话和代理
        const result = await this.sessionRepository.findByIdWithFullAgentInfo(sessionId, userId, agentId);

        if (!result) {
            throw new ValidationError('Session or Agent not found');
        }

        const { session: sessionEntity, agent } = result;
        const cardUrl = agent.cardUrl;

        if (cardUrl === null) {
            throw new ValidationError(`Agent with ID ${agentId} does not have a configured card URL.`);
        }

        if (sessionEntity.taskId === null) {
            throw new ValidationError('Task not found');
        }

        // 创建客户端并取消任务
        const client = new XuiA2AClient(cardUrl);
        const response = await client.cancelTask({
            id: sessionEntity.taskId
        } as TaskIdParams);

        Logger.info('Task cancelled successfully', {
            sessionId,
            agentId,
            userId,
            taskId: sessionEntity.taskId
        });

        return response;
    }
}
