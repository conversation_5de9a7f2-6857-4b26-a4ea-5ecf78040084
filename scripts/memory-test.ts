#!/usr/bin/env tsx

/**
 * 内存压力测试脚本
 * 
 * 专门测试并发聊天请求后的内存占用情况
 */

import { performance } from 'perf_hooks';

interface TestConfig {
    baseUrl: string;
    concurrentUsers: number;
    requestsPerUser: number;
    delayBetweenRequests: number;
    memoryCheckInterval: number;
}

interface MemorySnapshot {
    timestamp: number;
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
}

class MemoryStressTest {
    private readonly config: TestConfig;
    private readonly memorySnapshots: MemorySnapshot[] = [];
    private monitoringInterval: NodeJS.Timeout | null = null;

    constructor(config: TestConfig) {
        this.config = config;
    }

    /**
     * 开始内存监控
     */
    private startMemoryMonitoring(): void {
        console.log('🔍 Starting memory monitoring...');
        
        this.monitoringInterval = setInterval(() => {
            const mem = process.memoryUsage();
            const snapshot: MemorySnapshot = {
                timestamp: Date.now(),
                rss: Math.round(mem.rss / 1024 / 1024),
                heapTotal: Math.round(mem.heapTotal / 1024 / 1024),
                heapUsed: Math.round(mem.heapUsed / 1024 / 1024),
                external: Math.round(mem.external / 1024 / 1024),
                arrayBuffers: Math.round(mem.arrayBuffers / 1024 / 1024),
            };
            
            this.memorySnapshots.push(snapshot);
            
            console.log(
                `📊 Memory: RSS=${snapshot.rss}MB, ` +
                `Heap=${snapshot.heapUsed}/${snapshot.heapTotal}MB, ` +
                `External=${snapshot.external}MB`
            );
        }, this.config.memoryCheckInterval);
    }

    /**
     * 停止内存监控
     */
    private stopMemoryMonitoring(): void {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
    }

    /**
     * 使用固定的sessionId进行测试
     */
    private getFixedSessionId(): string {
        return '13dea014-35a3-4bd8-bd68-51ec511dcc92';
    }

    /**
     * 发送聊天消息 - 使用指定的测试参数
     */
    private async sendChatMessage(sessionId: string, userId: string): Promise<void> {
        const startTime = Date.now();

        const response = await globalThis.fetch(`${this.config.baseUrl}/api/session/mock/${sessionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'userId': 'test-001',
                'Accept': 'text/event-stream',
            },
            body: JSON.stringify({
                agentId: "c3c27aa8-2f15-452e-b842-8bfa4e818f9d",
                message: {
                    id: "fa8660ae-ae4d-4383-b8db-604ef5b002e9",
                    role: "user",
                    content: [
                        {
                            type: "data",
                            data: {
                                type: "action",
                                actionName: "common-session-start",
                                params: {
                                    sessionId: "1155292998125895681",
                                    projectId: "1155292998125895681"
                                }
                            }
                        }
                    ],
                    sender: {
                        id: "test-001",
                        name: "测试用户"
                    }
                }
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`❌ [${userId}] Request failed: ${response.status} - ${errorText}`);
            throw new Error(`Chat request failed: ${response.status} - ${errorText}`);
        }

        // 读取SSE流
        const reader = response.body?.getReader();
        if (!reader) {
            throw new Error('No response body');
        }

        let eventCount = 0;
        let totalDataSize = 0;

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) {
                    break;
                }

                // 解析SSE数据
                const decoder = new globalThis.TextDecoder();
                const chunk = decoder.decode(value);
                totalDataSize += chunk.length;

                // 解析SSE事件（简化日志，专注内存统计）
                const lines = chunk.split('\n');
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        eventCount++;
                        const eventData = line.substring(6);

                        // 检查结束事件
                        if (eventData.includes('session-finish') || eventData.includes('session-error')) {
                            const duration = Date.now() - startTime;
                            console.log(`🎯 [${userId}] Session completed in ${duration}ms, Events: ${eventCount}, Data: ${totalDataSize}B`);
                            return;
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
            const duration = Date.now() - startTime;
            console.log(`✅ [${userId}] Request completed in ${duration}ms, Events: ${eventCount}, Data size: ${totalDataSize} bytes`);
        }
    }

    /**
     * 模拟单个用户的聊天行为
     */
    private async simulateUser(userId: string): Promise<void> {
        try {
            // 使用固定的sessionId
            const sessionId = this.getFixedSessionId();

            // 发送多条消息
            for (let i = 0; i < this.config.requestsPerUser; i++) {
                await this.sendChatMessage(sessionId, userId);

                if (i < this.config.requestsPerUser - 1) {
                    await this.delay(this.config.delayBetweenRequests);
                }
            }
        } catch (error) {
            console.error(`❌ User ${userId} failed:`, error);
        }
    }

    /**
     * 延迟函数
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 运行并发测试
     */
    public async runTest(): Promise<void> {
        console.log('🚀 Starting Memory Stress Test');
        console.log(`📋 Config: ${this.config.concurrentUsers} users, ${this.config.requestsPerUser} requests each`);
        console.log('=' .repeat(60));

        // 记录初始内存
        const initialMemory = process.memoryUsage();
        console.log(`📊 Initial Memory: RSS=${Math.round(initialMemory.rss / 1024 / 1024)}MB, Heap=${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB`);

        // 开始内存监控
        this.startMemoryMonitoring();

        const startTime = performance.now();

        try {
            // 创建并发用户
            const userPromises = [];
            for (let i = 0; i < this.config.concurrentUsers; i++) {
                const userId = `test-user-${i.toString().padStart(3, '0')}`;
                userPromises.push(this.simulateUser(userId));
            }

            // 等待所有用户完成
            await Promise.allSettled(userPromises);

            const endTime = performance.now();
            const duration = Math.round(endTime - startTime);

            console.log('=' .repeat(60));
            console.log(`✅ Test completed in ${duration}ms`);

            // 等待一段时间观察内存变化
            console.log('⏳ Waiting 30 seconds to observe memory changes...');
            await this.delay(30000);

            // 强制垃圾回收（如果可用）
            if (global.gc) {
                console.log('🗑️  Forcing garbage collection...');
                global.gc();
                await this.delay(5000);
            }

            // 最终内存检查
            const finalMemory = process.memoryUsage();
            console.log(`📊 Final Memory: RSS=${Math.round(finalMemory.rss / 1024 / 1024)}MB, Heap=${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);

            // 分析内存变化
            this.analyzeMemoryUsage(initialMemory, finalMemory);

        } finally {
            this.stopMemoryMonitoring();
        }
    }

    /**
     * 分析内存使用情况
     */
    private analyzeMemoryUsage(initial: NodeJS.MemoryUsage, final: NodeJS.MemoryUsage): void {
        console.log('\n📈 Memory Analysis:');
        console.log('=' .repeat(40));

        const rssDiff = Math.round((final.rss - initial.rss) / 1024 / 1024);
        const heapDiff = Math.round((final.heapUsed - initial.heapUsed) / 1024 / 1024);
        const externalDiff = Math.round((final.external - initial.external) / 1024 / 1024);

        console.log(`RSS Change: ${rssDiff > 0 ? '+' : ''}${rssDiff}MB`);
        console.log(`Heap Change: ${heapDiff > 0 ? '+' : ''}${heapDiff}MB`);
        console.log(`External Change: ${externalDiff > 0 ? '+' : ''}${externalDiff}MB`);

        // 内存泄漏警告
        if (heapDiff > 50) {
            console.log('⚠️  WARNING: Significant heap memory increase detected!');
        }
        if (rssDiff > 100) {
            console.log('⚠️  WARNING: Significant RSS memory increase detected!');
        }

        // 峰值内存
        const peakMemory = this.memorySnapshots.reduce((max, snapshot) => 
            snapshot.heapUsed > max.heapUsed ? snapshot : max
        );
        console.log(`📊 Peak Heap Usage: ${peakMemory.heapUsed}MB`);
    }
}

// 运行测试
async function main(): Promise<void> {
    const config: TestConfig = {
        baseUrl: 'http://localhost:3000',
        concurrentUsers: 500,          // 500个并发用户（高负载测试）
        requestsPerUser: 1,            // 每个用户发送1条消息
        delayBetweenRequests: 500,     // 消息间隔0.5秒
        memoryCheckInterval: 1000,     // 每1秒检查一次内存
    };

    console.log('🧪 High Load Memory Stress Test Configuration:');
    console.log(`   📊 Concurrent Users: ${config.concurrentUsers}`);
    console.log(`   📝 Requests per User: ${config.requestsPerUser}`);
    console.log(`   ⏱️  Delay Between Requests: ${config.delayBetweenRequests}ms`);
    console.log(`   🔍 Memory Check Interval: ${config.memoryCheckInterval}ms`);
    console.log(`   🎯 Target Session: 13dea014-35a3-4bd8-bd68-51ec511dcc92`);
    console.log(`   👤 Test User: test-001`);
    console.log(`   � Environment: High Load Test (500 concurrent users)`);
    console.log('');

    const test = new MemoryStressTest(config);
    await test.runTest();
}

if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(console.error);
}
